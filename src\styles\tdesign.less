// tdesign的样式重置
.t-table th {
  background: #f5f7f9;
  color: #999999;
}
.t-table__header--fixed:not(.t-table__header--multiple) > tr > th {
  background: #f5f7f9;
  color: #999999;
}
.t-table__header {
  th {
    height: 40px;
    padding: 9px 16px;
  }
}

.t-menu {
  .t-menu__item {
    height: 38px;
    line-height: 38px;
  }
  .t-is-active {
    font-weight: 600;
  }
}

// tabs激活态的线高
.t-tabs__bar.t-is-top {
  height: 2.5px;
}

// 处理表单校验时的样式
.t-form__item {
  &:not(.t-form__item-with-extra) {
    margin-bottom: 16px;
  }
  .t-form__label {
    padding-right: 12px;
  }
}
// 修复最后一个form-item由于定位导致文本脱离文档流产生滚动条
.t-form:not(.t-form-inline) .t-form__item:last-of-type {
  .t-input__extra {
    position: initial;
  }
}
