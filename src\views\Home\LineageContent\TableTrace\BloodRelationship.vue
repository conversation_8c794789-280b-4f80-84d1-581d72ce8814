<script setup>
import { Graph, treeToGraphData } from '@antv/g6'
import { ref, onMounted } from 'vue'

const graph = ref(null)
const paintboardRef = ref(null)

const data = {
  id: 'Modeling Methods',
  children: [
    {
      id: 'Classification',
      children: [
        {
          id: 'Logistic regression',
        },
        {
          id: 'Linear discriminant analysis',
        },
        {
          id: 'Rules',
        },
        {
          id: 'Decision trees',
        },
        {
          id: 'Naive Bayes',
        },
        {
          id: 'K nearest neighbor',
        },
        {
          id: 'Probabilistic neural network',
        },
        {
          id: 'Support vector machine',
        },
      ],
    },
    {
      id: 'Consensus',
      children: [
        {
          id: 'Models diversity',
          children: [
            {
              id: 'Different initializations',
            },
            {
              id: 'Different parameter choices',
            },
            {
              id: 'Different architectures',
            },
            {
              id: 'Different modeling methods',
            },
            {
              id: 'Different training sets',
            },
            {
              id: 'Different feature sets',
            },
          ],
        },
        {
          id: 'Methods',
          children: [
            {
              id: 'Classifier selection',
            },
            {
              id: 'Classifier fusion',
            },
          ],
        },
        {
          id: 'Common',
          children: [
            {
              id: 'Bagging',
            },
            {
              id: 'Boosting',
            },
            {
              id: 'AdaBoost',
            },
          ],
        },
      ],
    },
    {
      id: 'Regression',
      children: [
        {
          id: 'Multiple linear regression',
        },
        {
          id: 'Partial least squares',
        },
        {
          id: 'Multi-layer feedforward neural network',
        },
        {
          id: 'General regression neural network',
        },
        {
          id: 'Support vector regression',
        },
      ],
    },
  ],
}
function isLeafNode(d) {
  return !d.children || d.children.length === 0
}

function initPaintboard() {
  graph.value = new Graph({
    container: paintboardRef.value,
    padding: 20,
    autoResize: true,
    autoFit: {
      type: 'view', // 自适应类型：'view' 或 'center'
      // options: {
      //   // 仅适用于 'view' 类型
      //   when: 'overflow', // 何时适配：'overflow'(仅当内容溢出时) 或 'always'(总是适配)
      //   direction: 'both', // 适配方向：'x'、'y' 或 'both'
      // },
      // animation: {
      //   // 自适应动画效果
      //   duration: 1000, // 动画持续时间(毫秒)
      //   easing: 'ease-in-out', // 动画缓动函数
      // },
    },
    data: treeToGraphData(data),
    node: {
      style: {
        labelText: (d) => d.id,
        labelPlacement: (d) => (isLeafNode(d) ? 'right' : 'left'),
        labelBackground: true,
        ports: [{ placement: 'right' }, { placement: 'left' }],
      },
      animation: {
        enter: false,
      },
    },
    edge: {
      type: 'cubic-horizontal',
      animation: {
        enter: false,
      },
    },
    layout: {
      type: 'dendrogram',
      direction: 'LR', // H / V / LR / RL / TB / BT
      nodeSep: 36,
      rankSep: 250,
    },
    behaviors: ['drag-canvas', 'zoom-canvas', 'drag-element', 'collapse-expand'],
  })

  graph.value.render()
}
onMounted(() => {
  initPaintboard()
})
</script>

<template>
  <div class="blood-relationship-page">
    <div ref="paintboardRef" class="paintboard"></div>
  </div>
</template>

<style lang="less" scoped>
.blood-relationship-page {
  margin-top: 20px;
  height: calc(100% - 20px);
  background-color: #eee;
  .paintboard {
    height: 100%;
  }
}
</style>
