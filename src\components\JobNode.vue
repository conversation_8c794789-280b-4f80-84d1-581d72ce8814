<script setup>
import { computed } from 'vue'
import {
  DataBaseIcon,
  TableIcon,
  ControlPlatformIcon,
  LogoWindowsIcon,
} from 'tdesign-icons-vue-next'
// 按实际情况调整
// DataBaseIcon 数据源 数据模型 上游表 参照目标表 数据表
// TableIcon 参照字段 字段
// ControlPlatformIcon 下游接口 接口 参照接口
// LogoWindowsIcon 应用

const props = defineProps({
  data: Object,
})

const nodeType = computed(() => props.data.data.type)

const componentName = computed(() => {
  switch (nodeType.value) {
    case 'datasource':
      return DataBaseIcon
    case 'table':
      return TableIcon
    case 'field':
      return TableIcon
    case 'interface':
      return ControlPlatformIcon
    case 'application':
      return LogoWindowsIcon
    default:
      return DataBaseIcon
  }
})

// hover-activate
const isActive = computed(() => props.data.states?.includes('active'))
// click-select
const isSelected = computed(() => props.data.states?.includes('selected'))
// hover-activate & click-select
const isActived = computed(() => isActive.value || isSelected.value)

function handleNodeClick() {
  console.log('click', props.data.id)
}
</script>

<template>
  <div class="job-node" :class="{ active: isActived }" @click="handleNodeClick">
    <component :is="componentName" class="job-node-icon" :class="{ 'icon-active': isActived }" />
    <span class="job-node-label">{{ props.data.id }}</span>
  </div>
</template>

<style lang="less" scoped>
.job-node {
  width: 240px;
  height: 40px;
  color: #333;
  background: #fff;
  border-radius: 8px;
  display: flex;
  align-items: center;
  padding: 10px;
  border: 1px solid #ccc;
  .job-node-icon {
    color: var(--trace-lineage-color-theme);
    margin-right: 5px;
  }
  .job-node-label {
    flex: 1;
  }
}
.active {
  color: #fff;
  background: #6696f6;
}
.icon-active {
  color: #fff !important;
}
</style>
