import { createRouter, createWebHistory } from 'vue-router'
import MasterPage from '@/MasterPage.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'root',
      redirect: { name: 'lineage' },
      component: MasterPage,
      children: [
        {
          path: '/home',
          name: 'home',
          component: () => import('@/views/Home/MainPage.vue'),
          children: [
            {
              path: 'lineage',
              name: 'lineage',
              redirect: { name: 'table' },
              component: () => import('@/views/Home/lineage/MainPage.vue'),
              children: [
                {
                  path: 'table',
                  name: 'table',
                  component: () => import('@/views/Home/lineage/TablePage.vue'),
                },
                {
                  path: 'field',
                  name: 'field',
                  component: () => import('@/views/Home/lineage/FieldPage.vue'),
                },
                {
                  path: 'interface',
                  name: 'interface',
                  component: () => import('@/views/Home/lineage/InterfacePage.vue'),
                },
              ],
            },
          ],
        },
      ],
    },
  ],
})

export default router
