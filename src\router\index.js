import { createRouter, createWebHistory } from 'vue-router'
import MasterPage from '@/MasterPage.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'root',
      redirect: { name: 'home' },
      component: MasterPage,
      children: [
        {
          path: '/home',
          name: 'home',
          redirect: { name: 'lineage' },
          component: () => import('@/views/Home/MainPage.vue'),
          children: [
            {
              path: 'lineage',
              name: 'lineage',
              redirect: { name: 'table' },
              component: () => import('@/views/Home/Lineage/MainPage.vue'),
              children: [
                {
                  path: 'table',
                  name: 'table',
                  component: () => import('@/views/Home/Lineage/TablePage.vue'),
                  meta: { sidebarKey: 'lineage' },
                },
                {
                  path: 'field',
                  name: 'field',
                  component: () => import('@/views/Home/Lineage/FieldPage.vue'),
                  meta: { sidebarKey: 'lineage' },
                },
                {
                  path: 'interface',
                  name: 'interface',
                  component: () => import('@/views/Home/Lineage/InterfacePage.vue'),
                  meta: { sidebarKey: 'lineage' },
                },
              ],
            },
            {
              path: 'tableTrace',
              name: 'tableTrace',
              component: () => import('@/views/Home/LineageContent/TableTrace/MainPage.vue'),
              meta: { sidebarKey: 'lineage' },
            },
          ],
        },
      ],
    },
  ],
})

export default router
