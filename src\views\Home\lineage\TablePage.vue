<script setup>
import { ref } from 'vue'

const form = ref({
  tableName: '',
  datasource: '',
  updateTime: [],
})

const tableData = ref([
  {
    id: 1,
    tableName: '表名1',
    datasource: '数据源1',
    updateTime: '2023-01-01 00:00:00',
  },
  {
    id: 2,
    tableName: '表名2',
    datasource: '数据源2',
    updateTime: '2023-01-01 00:00:00',
  },
])

const columns = ref([
  {
    title: '表名',
    colKey: 'tableName',
  },
  {
    title: '数据源',
    colKey: 'datasource',
  },
  {
    title: '更新时间',
    colKey: 'updateTime',
  },
])

const pagination = ref({
  defaultPageSize: 10,
  total: 100,
  showJumper: true,
  showPageSize: true,
  showTotal: true,
})
</script>

<template>
  <div class="trace-lineage-table-page">
    <div class="serach-wrap">
      <t-form :data="form" layout="inline" labelAlign="left" labelWidth="10">
        <t-form-item label="表名">
          <t-input v-model="form.tableName"></t-input>
        </t-form-item>
        <t-form-item label="数据源">
          <t-select v-model="form.datasource" :options="[]"></t-select>
        </t-form-item>
        <t-form-item label="更新时间">
          <t-date-range-picker
            v-model="form.updateTime"
            enable-time-picker
            allow-input
            clearable
            :placeholder="['开始时间', '结束时间']"
          />
        </t-form-item>
        <t-form-item>
          <t-space>
            <t-button variant="outline">查询</t-button>
            <t-button variant="outline">重置</t-button>
          </t-space>
        </t-form-item>
      </t-form>
    </div>

    <t-table
      row-key="id"
      :data="tableData"
      :columns="columns"
      :pagination="pagination"
      max-height="100%"
    ></t-table>
  </div>
</template>

<style lang="less" scoped>
.trace-lineage-table-page {
  height: 100%;
  .serach-wrap {
    margin-bottom: var(--trace-lineage-margin);
    :deep(.t-form__item) {
      &:not(.t-form__item-with-extra) {
        margin-bottom: 0;
      }
    }
  }
  :deep(.t-table) {
    height: calc(100% - 32px - var(--trace-lineage-margin));
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
}
</style>
