<script setup>
import BlockHeader from '@/components/BlockHeader.vue'
import CommonBreadcrumb from '@/components/CommonBreadcrumb.vue'
import BloodRelationship from './BloodRelationship.vue'
import { ref } from 'vue'

const actived = ref('relationship')
</script>

<template>
  <div class="table-trace-page">
    <CommonBreadcrumb :breadcrumbs="['数据血缘', '表血缘详情']"></CommonBreadcrumb>
    <div class="table-trace-page__content">
      <div class="table-trace-page__header">
        <BlockHeader title="血缘信息"></BlockHeader>
        <span>（xxxxx）</span>
      </div>
      <div class="table-trace-page__main">
        <t-tabs v-model="actived">
          <t-tab-panel value="relationship" label="血缘关系图">
            <BloodRelationship></BloodRelationship>
          </t-tab-panel>
          <t-tab-panel value="usage" label="使用情况">
            <p>88888888</p>
          </t-tab-panel>
        </t-tabs>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.table-trace-page {
  height: 100%;
  .table-trace-page__content {
    margin: var(--trace-lineage-margin);
    padding: var(--trace-lineage-padding);
    // 42px 为 breadcrumb 高度 + 上下margin + 上下padding
    height: calc(100% - 42px - (var(--trace-lineage-margin) * 4));
    background-color: #fff;
    .table-trace-page__header {
      display: flex;
      align-items: center;
    }
    .table-trace-page__main {
      padding: var(--trace-lineage-padding);
      height: calc(100% - 22px - (var(--trace-lineage-padding) * 2));
      .t-tabs {
        height: 100%;
      }
      :deep(.t-tabs > .t-tabs__header) {
        .t-tabs__nav-item {
          height: 38px;
          line-height: 38px;
        }
      }
      :deep(.t-tabs__content) {
        height: calc(100% - 38px);
        .t-tab-panel {
          height: 100%;
        }
      }
    }
  }
}
</style>
