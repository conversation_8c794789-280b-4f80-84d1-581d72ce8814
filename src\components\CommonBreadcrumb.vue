<template>
  <div class="trace-lineage__breadcrumb">
    <t-breadcrumb :maxItemWidth="'150'">
      <t-breadcrumbItem v-for="(item, index) in breadcrumbs" :key="index">
        {{ item }}
      </t-breadcrumbItem>
    </t-breadcrumb>
  </div>
</template>

<script setup>
defineProps({
  breadcrumbs: {
    type: Array,
    default: () => [],
    required: true,
  },
})
</script>

<style lang="less" scoped>
.trace-lineage__breadcrumb {
  padding: var(--trace-lineage-padding);
  background-color: #fff;
}
</style>
