<template>
  <t-menu theme="light" width="210px" v-model:value="actived" @change="handleMenuChange">
    <t-menu-item value="lineage">
      <template #icon>
        <SitemapIcon />
      </template>
      <span class="menu-text">数据血缘</span>
    </t-menu-item>
  </t-menu>
</template>

<script setup>
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { SitemapIcon } from 'tdesign-icons-vue-next'

const route = useRoute()
const router = useRouter()
const rootRouteName = route.path.split('/')[2]
const actived = ref(rootRouteName)

function handleMenuChange(active) {
  router.push({ name: active })
}
</script>

<style lang="less" scoped>
.t-menu {
  :deep(.t-menu__item) {
    height: 38px;
    &:nth-child(n + 2) {
      margin-top: 8px;
    }
  }
  .menu-text {
    margin-left: 12px;
  }
}
</style>
