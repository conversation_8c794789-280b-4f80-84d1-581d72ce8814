<template>
  <div class="trace-lineage-common-header">
    <header class="header-main header-inner">
      <div class="header-left">
        <slot name="header-left"></slot>
      </div>
      <div class="header-center">
        <slot name="header-center"></slot>
      </div>
      <div class="header-right">
        <slot name="header-right"></slot>
        <t-dropdown @click="handleCommand">
          <div class="header-avatar">
            <t-avatar :image="avatar">
              <img src="@/assets/avatar.png" />
            </t-avatar>
            <div style="margin-left: 8px">
              <ChevronDownIcon />
            </div>
          </div>
          <template v-slot:dropdown>
            <t-dropdown-menu class="trace-lineage-header__dropdown-menu">
              <t-dropdown-item value="logout">退出</t-dropdown-item>
            </t-dropdown-menu>
          </template>
        </t-dropdown>
      </div>
    </header>
    <div class="header-bottom">
      <slot name="header-bottom"></slot>
    </div>
  </div>
</template>

<script setup>
import { ChevronDownIcon } from 'tdesign-icons-vue-next'

defineProps({
  avatar: {
    type: String,
  },
})
const emit = defineEmits(['logout'])

function handleCommand(item) {
  if (item.value === 'logout') {
    emit('logout')
  }
}
</script>

<style lang="less" scoped>
.trace-lineage-common-header {
  background: linear-gradient(to right, #3464e0, #1890ff);
  .header-main {
    display: flex;
    height: 60px;
    justify-content: space-between;
  }
  .header-left {
    padding-left: 20px;
  }
  .header-center {
    flex: 1;
  }
  .header-right {
    display: flex;
    align-items: center;
    padding-right: 30px;
    color: #fff;
  }
  .header-avatar {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #fff;
    &:hover {
      .t-icon-chevron-down {
        transform: rotate(180deg);
      }
    }
    .t-icon-chevron-down {
      transition: all ease-in-out 0.3s;
    }
  }
}
.trace-lineage-header__dropdown-menu {
  min-width: 100px;
}
</style>
